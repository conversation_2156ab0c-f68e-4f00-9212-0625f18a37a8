"""
错误处理和日志模块
提供统一的错误处理和日志记录功能
"""

import logging
import traceback
import sys
from datetime import datetime
from pathlib import Path
from functools import wraps
import json
from typing import Dict, Any, Optional
import threading

from config import get_config

class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)

class LogManager:
    """日志管理器"""
    
    def __init__(self, config_name=None):
        self.config = get_config(config_name)
        self.loggers = {}
        self.lock = threading.Lock()
        
        # 确保日志目录存在
        self.config.LOGS_DIR.mkdir(exist_ok=True)
        
        # 设置根日志记录器
        self.setup_root_logger()
    
    def setup_root_logger(self):
        """设置根日志记录器"""
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, self.config.LOG_LEVEL))
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 文件处理器
        file_handler = logging.FileHandler(
            self.config.LOG_FILE,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(self.config.LOG_FORMAT)
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, self.config.LOG_LEVEL))
        console_formatter = ColoredFormatter(self.config.LOG_FORMAT)
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
    
    def get_logger(self, name):
        """获取指定名称的日志记录器"""
        with self.lock:
            if name not in self.loggers:
                logger = logging.getLogger(name)
                self.loggers[name] = logger
            return self.loggers[name]
    
    def create_error_log(self, error_type, error_message, context=None):
        """创建错误日志条目"""
        error_entry = {
            'timestamp': datetime.now().isoformat(),
            'error_type': error_type,
            'error_message': str(error_message),
            'context': context or {},
            'traceback': traceback.format_exc() if sys.exc_info()[0] else None
        }
        
        # 保存到错误日志文件
        error_log_file = self.config.LOGS_DIR / 'errors.json'
        
        try:
            # 读取现有错误日志
            errors = []
            if error_log_file.exists():
                with open(error_log_file, 'r', encoding='utf-8') as f:
                    errors = json.load(f)
            
            # 添加新错误
            errors.append(error_entry)
            
            # 限制错误日志数量（保留最近1000条）
            if len(errors) > 1000:
                errors = errors[-1000:]
            
            # 保存错误日志
            with open(error_log_file, 'w', encoding='utf-8') as f:
                json.dump(errors, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            # 如果无法保存错误日志，至少记录到标准日志
            logger = self.get_logger(__name__)
            logger.error(f"无法保存错误日志: {e}")
        
        return error_entry

# 全局日志管理器实例
log_manager = LogManager()

def get_logger(name):
    """获取日志记录器的便捷函数"""
    return log_manager.get_logger(name)

class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.error_callbacks = []
    
    def add_error_callback(self, callback):
        """添加错误回调函数"""
        self.error_callbacks.append(callback)
    
    def handle_error(self, error, context=None, reraise=True):
        """
        处理错误
        
        Args:
            error: 错误对象
            context: 错误上下文信息
            reraise: 是否重新抛出错误
        """
        error_type = type(error).__name__
        error_message = str(error)
        
        # 记录错误日志
        self.logger.error(f"{error_type}: {error_message}", exc_info=True)
        
        # 创建错误日志条目
        error_entry = log_manager.create_error_log(error_type, error_message, context)
        
        # 调用错误回调函数
        for callback in self.error_callbacks:
            try:
                callback(error_entry)
            except Exception as callback_error:
                self.logger.error(f"错误回调函数执行失败: {callback_error}")
        
        if reraise:
            raise error
        
        return error_entry

# 全局错误处理器实例
error_handler = ErrorHandler()

def handle_exceptions(reraise=True, context=None):
    """
    异常处理装饰器
    
    Args:
        reraise: 是否重新抛出异常
        context: 额外的上下文信息
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 构建上下文信息
                func_context = {
                    'function': func.__name__,
                    'module': func.__module__,
                    'args': str(args)[:200],  # 限制长度
                    'kwargs': str(kwargs)[:200]
                }
                
                if context:
                    func_context.update(context)
                
                # 处理错误
                error_handler.handle_error(e, func_context, reraise)
                
                if not reraise:
                    return None
        
        return wrapper
    return decorator

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.performance")
        self.metrics = {}
        self.lock = threading.Lock()
    
    def record_metric(self, name, value, unit='', tags=None):
        """
        记录性能指标
        
        Args:
            name: 指标名称
            value: 指标值
            unit: 单位
            tags: 标签
        """
        with self.lock:
            metric_entry = {
                'timestamp': datetime.now().isoformat(),
                'name': name,
                'value': value,
                'unit': unit,
                'tags': tags or {}
            }
            
            if name not in self.metrics:
                self.metrics[name] = []
            
            self.metrics[name].append(metric_entry)
            
            # 限制每个指标的历史记录数量
            if len(self.metrics[name]) > 1000:
                self.metrics[name] = self.metrics[name][-1000:]
            
            self.logger.debug(f"性能指标 {name}: {value} {unit}")
    
    def get_metrics(self, name=None):
        """获取性能指标"""
        with self.lock:
            if name:
                return self.metrics.get(name, [])
            return self.metrics.copy()
    
    def clear_metrics(self, name=None):
        """清除性能指标"""
        with self.lock:
            if name:
                self.metrics.pop(name, None)
            else:
                self.metrics.clear()

# 全局性能监控器实例
performance_monitor = PerformanceMonitor()

def monitor_performance(metric_name, unit=''):
    """
    性能监控装饰器
    
    Args:
        metric_name: 指标名称
        unit: 单位
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = datetime.now()
            
            try:
                result = func(*args, **kwargs)
                
                # 记录执行时间
                execution_time = (datetime.now() - start_time).total_seconds()
                performance_monitor.record_metric(
                    f"{metric_name}_execution_time",
                    execution_time,
                    'seconds',
                    {'function': func.__name__, 'status': 'success'}
                )
                
                return result
                
            except Exception as e:
                # 记录失败的执行时间
                execution_time = (datetime.now() - start_time).total_seconds()
                performance_monitor.record_metric(
                    f"{metric_name}_execution_time",
                    execution_time,
                    'seconds',
                    {'function': func.__name__, 'status': 'error'}
                )
                
                raise
        
        return wrapper
    return decorator

class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.health")
        self.checks = {}
    
    def register_check(self, name, check_func, interval=60):
        """
        注册健康检查
        
        Args:
            name: 检查名称
            check_func: 检查函数，返回(is_healthy, message)
            interval: 检查间隔（秒）
        """
        self.checks[name] = {
            'func': check_func,
            'interval': interval,
            'last_check': None,
            'last_result': None
        }
    
    def run_check(self, name):
        """运行指定的健康检查"""
        if name not in self.checks:
            return False, f"未找到健康检查: {name}"
        
        check = self.checks[name]
        
        try:
            is_healthy, message = check['func']()
            check['last_check'] = datetime.now()
            check['last_result'] = (is_healthy, message)
            
            if not is_healthy:
                self.logger.warning(f"健康检查失败 {name}: {message}")
            
            return is_healthy, message
            
        except Exception as e:
            error_message = f"健康检查异常 {name}: {e}"
            self.logger.error(error_message)
            check['last_check'] = datetime.now()
            check['last_result'] = (False, error_message)
            
            return False, error_message
    
    def run_all_checks(self):
        """运行所有健康检查"""
        results = {}
        
        for name in self.checks:
            is_healthy, message = self.run_check(name)
            results[name] = {
                'healthy': is_healthy,
                'message': message,
                'timestamp': datetime.now().isoformat()
            }
        
        return results
    
    def get_health_status(self):
        """获取整体健康状态"""
        results = self.run_all_checks()
        
        overall_healthy = all(result['healthy'] for result in results.values())
        
        return {
            'healthy': overall_healthy,
            'checks': results,
            'timestamp': datetime.now().isoformat()
        }

# 全局健康检查器实例
health_checker = HealthChecker()

if __name__ == "__main__":
    # 测试日志功能
    logger = get_logger(__name__)
    
    logger.debug("这是调试信息")
    logger.info("这是信息")
    logger.warning("这是警告")
    logger.error("这是错误")
    
    # 测试错误处理
    @handle_exceptions(reraise=False)
    def test_function():
        raise ValueError("测试错误")
    
    test_function()
    
    # 测试性能监控
    @monitor_performance("test_operation")
    def test_performance():
        import time
        time.sleep(0.1)
        return "完成"
    
    result = test_performance()
    print(f"性能测试结果: {result}")
    
    # 显示性能指标
    metrics = performance_monitor.get_metrics("test_operation_execution_time")
    print(f"性能指标: {metrics}")
