/**
 * 裁判文书数据导入系统前端JavaScript
 */

class ImportApp {
    constructor() {
        this.socket = null;
        this.yearlyChart = null;
        this.isConnected = false;
        this.logMessages = [];
        this.maxLogMessages = 100;
        
        this.init();
    }
    
    init() {
        this.initSocket();
        this.initEventListeners();
        this.initCharts();
        this.loadInitialData();
        
        // 定期更新状态
        setInterval(() => {
            if (this.isConnected) {
                this.socket.emit('request_status');
            }
        }, 5000);
    }
    
    initSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('WebSocket连接成功');
            this.isConnected = true;
            this.addLog('WebSocket连接成功', 'success');
        });
        
        this.socket.on('disconnect', () => {
            console.log('WebSocket连接断开');
            this.isConnected = false;
            this.addLog('WebSocket连接断开', 'warning');
        });
        
        this.socket.on('status_update', (data) => {
            this.updateStatus(data);
        });
        
        this.socket.on('error', (data) => {
            console.error('WebSocket错误:', data);
            this.addLog(`错误: ${data.message}`, 'error');
        });
    }
    
    initEventListeners() {
        // 开始导入按钮
        document.getElementById('startBtn').addEventListener('click', () => {
            this.startImport(false);
        });
        
        // 断点续传按钮
        document.getElementById('resumeBtn').addEventListener('click', () => {
            this.startImport(true);
        });
        
        // 停止导入按钮
        document.getElementById('stopBtn').addEventListener('click', () => {
            this.stopImport();
        });
        
        // 清除进度按钮
        document.getElementById('clearBtn').addEventListener('click', () => {
            this.clearProgress();
        });
    }
    
    initCharts() {
        const ctx = document.getElementById('yearlyChart').getContext('2d');
        this.yearlyChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: '记录数量',
                    data: [],
                    backgroundColor: 'rgba(13, 110, 253, 0.8)',
                    borderColor: 'rgba(13, 110, 253, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
    
    async loadInitialData() {
        try {
            // 加载状态信息
            const response = await fetch('/api/status');
            const data = await response.json();
            this.updateStatus(data);
            
            // 加载文件列表
            await this.loadFileList();
            
            // 加载数据库统计
            await this.loadDatabaseStats();
            
        } catch (error) {
            console.error('加载初始数据失败:', error);
            this.addLog(`加载初始数据失败: ${error.message}`, 'error');
        }
    }
    
    async loadFileList() {
        try {
            const response = await fetch('/api/files');
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            this.renderFileList(data.files);
            document.getElementById('totalFiles').textContent = data.total_files;
            
        } catch (error) {
            console.error('加载文件列表失败:', error);
            this.addLog(`加载文件列表失败: ${error.message}`, 'error');
        }
    }
    
    async loadDatabaseStats() {
        try {
            const response = await fetch('/api/database_stats');
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            this.updateDatabaseStats(data);
            
        } catch (error) {
            console.error('加载数据库统计失败:', error);
            this.addLog(`加载数据库统计失败: ${error.message}`, 'error');
        }
    }
    
    renderFileList(files) {
        const tbody = document.getElementById('fileList');
        tbody.innerHTML = '';
        
        files.forEach(file => {
            const row = document.createElement('tr');
            row.className = 'file-item';
            
            // 状态图标
            let statusIcon = '';
            let statusClass = '';
            if (file.completed || file.imported) {
                statusIcon = '<i class="bi bi-check-circle-fill text-success"></i>';
                statusClass = 'table-success';
            } else {
                statusIcon = '<i class="bi bi-circle text-muted"></i>';
            }
            
            // 文件大小格式化
            const fileSize = this.formatFileSize(file.file_size);
            
            row.innerHTML = `
                <td>
                    ${statusIcon}
                    <span class="ms-2">${file.file_name}</span>
                </td>
                <td>${file.year || '--'}年${file.month || '--'}月</td>
                <td>${fileSize}</td>
                <td>
                    ${file.imported ? 
                        `<span class="badge bg-success">已导入 (${file.imported_count})</span>` : 
                        '<span class="badge bg-secondary">未导入</span>'
                    }
                </td>
            `;
            
            if (statusClass) {
                row.className += ` ${statusClass}`;
            }
            
            tbody.appendChild(row);
        });
    }
    
    updateStatus(data) {
        const importStatus = data.import_status || {};
        const progressSummary = data.progress_summary || {};
        const databaseStats = data.database_stats || {};
        
        // 更新基本指标
        if (importStatus.total_files !== undefined) {
            document.getElementById('totalFiles').textContent = importStatus.total_files;
        }
        if (progressSummary.processed_files !== undefined) {
            document.getElementById('processedFiles').textContent = progressSummary.processed_files;
        }
        if (progressSummary.total_records !== undefined) {
            document.getElementById('totalRecords').textContent = progressSummary.total_records.toLocaleString();
        }
        if (importStatus.current_speed !== undefined) {
            document.getElementById('processingSpeed').textContent = Math.round(importStatus.current_speed);
        }
        
        // 更新进度条
        const progress = progressSummary.progress_percent || 0;
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        
        progressBar.style.width = `${progress}%`;
        progressText.textContent = `${progress.toFixed(1)}%`;
        
        if (importStatus.is_importing) {
            progressBar.classList.add('progress-bar-animated');
        } else {
            progressBar.classList.remove('progress-bar-animated');
        }
        
        // 更新状态指示器
        this.updateStatusIndicator(progressSummary.status || 'idle');
        
        // 更新当前文件
        const currentFile = importStatus.current_file || progressSummary.current_file || '等待开始...';
        document.getElementById('currentFile').textContent = currentFile;
        
        // 更新时间信息
        document.getElementById('elapsedTime').textContent = 
            this.formatTime(progressSummary.elapsed_time || 0);
        document.getElementById('remainingTime').textContent = 
            this.formatTime(progressSummary.eta_seconds || 0);
        
        // 更新记录数
        document.getElementById('successRecords').textContent = 
            (progressSummary.processed_records || 0).toLocaleString();
        document.getElementById('failedRecords').textContent = 
            (progressSummary.failed_records || 0).toLocaleString();
        
        // 更新按钮状态
        this.updateButtonStates(importStatus.is_importing || false);
        
        // 更新数据库统计
        if (databaseStats.document_count !== undefined) {
            document.getElementById('dbRecordCount').textContent = 
                databaseStats.document_count.toLocaleString();
        }
        if (databaseStats.storage_size !== undefined) {
            document.getElementById('dbStorageSize').textContent = 
                this.formatFileSize(databaseStats.storage_size);
        }
        
        // 添加日志
        if (importStatus.is_importing && currentFile !== '等待开始...') {
            this.addLog(`正在处理: ${currentFile}`, 'info');
        }
    }
    
    updateStatusIndicator(status) {
        const indicator = document.getElementById('statusIndicator');
        indicator.className = `status-indicator status-${status}`;
    }
    
    updateButtonStates(isImporting) {
        document.getElementById('startBtn').disabled = isImporting;
        document.getElementById('resumeBtn').disabled = isImporting;
        document.getElementById('stopBtn').disabled = !isImporting;
        document.getElementById('clearBtn').disabled = isImporting;
    }
    
    updateDatabaseStats(data) {
        // 更新年度统计图表
        if (data.yearly_stats && this.yearlyChart) {
            const labels = data.yearly_stats.map(item => `${item._id}年`);
            const counts = data.yearly_stats.map(item => item.count);
            
            this.yearlyChart.data.labels = labels;
            this.yearlyChart.data.datasets[0].data = counts;
            this.yearlyChart.update();
        }
    }
    
    async startImport(resume = false) {
        try {
            const response = await fetch('/api/start_import', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ resume })
            });
            
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            this.addLog(data.message, 'success');
            
        } catch (error) {
            console.error('开始导入失败:', error);
            this.addLog(`开始导入失败: ${error.message}`, 'error');
        }
    }
    
    async stopImport() {
        try {
            const response = await fetch('/api/stop_import', {
                method: 'POST'
            });
            
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            this.addLog(data.message, 'warning');
            
        } catch (error) {
            console.error('停止导入失败:', error);
            this.addLog(`停止导入失败: ${error.message}`, 'error');
        }
    }
    
    async clearProgress() {
        if (!confirm('确定要清除所有进度数据吗？此操作不可恢复。')) {
            return;
        }
        
        try {
            const response = await fetch('/api/clear_progress', {
                method: 'POST'
            });
            
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            this.addLog(data.message, 'info');
            
            // 重新加载数据
            setTimeout(() => {
                this.loadInitialData();
            }, 1000);
            
        } catch (error) {
            console.error('清除进度失败:', error);
            this.addLog(`清除进度失败: ${error.message}`, 'error');
        }
    }
    
    addLog(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = {
            timestamp,
            message,
            type
        };
        
        this.logMessages.push(logEntry);
        
        // 限制日志数量
        if (this.logMessages.length > this.maxLogMessages) {
            this.logMessages.shift();
        }
        
        this.renderLogs();
    }
    
    renderLogs() {
        const container = document.getElementById('logContainer');
        
        const html = this.logMessages.map(log => {
            let className = '';
            let icon = '';
            
            switch (log.type) {
                case 'success':
                    className = 'text-success';
                    icon = '✓';
                    break;
                case 'error':
                    className = 'text-danger';
                    icon = '✗';
                    break;
                case 'warning':
                    className = 'text-warning';
                    icon = '⚠';
                    break;
                default:
                    className = 'text-info';
                    icon = 'ℹ';
            }
            
            return `<div class="${className}">
                [${log.timestamp}] ${icon} ${log.message}
            </div>`;
        }).join('');
        
        container.innerHTML = html;
        container.scrollTop = container.scrollHeight;
    }
    
    formatTime(seconds) {
        if (!seconds || seconds <= 0) {
            return '--:--:--';
        }
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ImportApp();
});
