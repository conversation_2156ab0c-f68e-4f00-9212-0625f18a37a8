# 裁判文书数据导入系统

一个功能完整的裁判文书数据导入系统，支持大规模CSV文件批量导入到MongoDB，具有Web可视化界面、实时进度监控、断点续传等功能。

## 功能特性

- 🗄️ **MongoDB数据存储**: 优化的数据结构和索引设计
- 📊 **Web可视化界面**: 实时监控导入进度和系统状态
- 🔄 **断点续传**: 支持从中断位置继续导入
- ⚡ **批量处理**: 高效的批量插入和并发处理
- 📈 **实时统计**: 处理速度、进度百分比、剩余时间估算
- 🛡️ **错误处理**: 完善的错误处理和日志记录
- 🏥 **健康检查**: 系统组件健康状态监控
- 📱 **响应式界面**: 支持多种设备的现代化Web界面

## 系统要求

- Python 3.8+
- MongoDB 4.4+
- 8GB+ RAM (推荐)
- 足够的磁盘空间存储数据

## 安装步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd judgment-documents-importer
```

### 2. 创建虚拟环境
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 配置MongoDB
确保MongoDB服务正在运行，默认连接地址为 `mongodb://localhost:27017/`

### 5. 准备数据文件
将CSV文件按以下结构组织：
```
裁判文书/
├── 2021年/
│   ├── 2021年01月裁判文书数据.csv
│   ├── 2021年02月裁判文书数据.csv
│   └── ...
├── 2022年/
│   ├── 2022年01月裁判文书数据.csv
│   └── ...
└── ...
```

## 使用方法

### 命令行模式

#### 1. 初始化数据库
```bash
python main.py init
```

#### 2. 导入数据
```bash
# 全新导入
python main.py import --no-resume

# 断点续传（默认）
python main.py import
```

#### 3. 启动Web界面
```bash
python main.py web
```

#### 4. 健康检查
```bash
python main.py health
```

### Web界面模式

1. 启动Web服务器：
```bash
python main.py web
```

2. 打开浏览器访问：`http://localhost:5000`

3. 使用Web界面控制导入过程：
   - 点击"开始导入"开始全新导入
   - 点击"断点续传"从上次中断位置继续
   - 点击"停止导入"暂停当前导入
   - 点击"清除进度"重置所有进度数据

## 配置选项

### 环境配置
```bash
# 开发环境（默认）
python main.py web --config development

# 生产环境
python main.py web --config production

# 测试环境
python main.py web --config testing
```

### 主要配置参数

在 `config.py` 中可以调整以下参数：

- `MONGODB_URI`: MongoDB连接字符串
- `BATCH_SIZE`: 批量插入大小（默认1000）
- `MAX_WORKERS`: 最大工作线程数（默认4）
- `CHUNK_SIZE`: CSV文件分块读取大小（默认10000）

## 数据结构

### CSV文件格式
系统期望的CSV文件包含以下字段：
- 原始链接
- 案号
- 案件名称
- 法院
- 所属地区
- 案件类型
- 案件类型编码
- 来源
- 审理程序
- 裁判日期
- 公开日期
- 当事人
- 案由
- 法律依据
- 全文

### MongoDB文档结构
```json
{
  "_id": "ObjectId",
  "original_url": "String",
  "case_number": "String",
  "case_name": "String",
  "court": "String",
  "region": "String",
  "case_type": "String",
  "case_type_code": "String",
  "source": "String",
  "trial_procedure": "String",
  "judgment_date": "Date",
  "publish_date": "Date",
  "parties": "String",
  "case_cause": "String",
  "legal_basis": "String",
  "full_text": "String",
  "import_time": "Date",
  "file_source": "String",
  "year": "Integer",
  "month": "Integer",
  "text_length": "Integer",
  "keywords": ["String"]
}
```

## 性能优化

### 数据库索引
系统自动创建以下索引：
- 案号唯一索引（防重复）
- 年份+月份复合索引
- 法院、地区、案件类型索引
- 全文搜索索引

### 导入性能
- 批量插入减少数据库操作
- 并发处理提高吞吐量
- 内存管理避免OOM
- 断点续传避免重复工作

## 监控和日志

### 日志文件
- `logs/import.log`: 主要日志文件
- `logs/errors.json`: 错误日志（JSON格式）

### 监控指标
- 处理速度（条/秒）
- 内存使用率
- 磁盘空间
- 数据库连接状态

## 故障排除

### 常见问题

1. **MongoDB连接失败**
   - 检查MongoDB服务是否运行
   - 验证连接字符串是否正确
   - 检查网络连接和防火墙设置

2. **内存不足**
   - 减少 `BATCH_SIZE` 和 `CHUNK_SIZE`
   - 减少 `MAX_WORKERS` 数量
   - 增加系统内存

3. **文件读取错误**
   - 检查文件编码（默认UTF-8）
   - 验证CSV文件格式
   - 确保文件权限正确

4. **导入速度慢**
   - 增加 `BATCH_SIZE`（在内存允许范围内）
   - 检查磁盘I/O性能
   - 优化MongoDB配置

### 日志分析
```bash
# 查看最新日志
tail -f logs/import.log

# 搜索错误
grep "ERROR" logs/import.log

# 查看错误详情
cat logs/errors.json | jq '.'
```

## 开发和扩展

### 项目结构
```
├── main.py              # 主启动脚本
├── config.py            # 配置管理
├── database_design.py   # 数据库设计
├── data_importer.py     # 数据导入核心
├── progress_manager.py  # 进度管理
├── error_handler.py     # 错误处理
├── web_app.py          # Web应用
├── templates/          # HTML模板
├── static/            # 静态资源
├── logs/              # 日志文件
└── 裁判文书/           # 数据文件目录
```

### 添加新功能
1. 在相应模块中添加功能代码
2. 更新Web API（如需要）
3. 更新前端界面（如需要）
4. 添加测试用例
5. 更新文档

## 许可证

[添加许可证信息]

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

[添加联系方式]
