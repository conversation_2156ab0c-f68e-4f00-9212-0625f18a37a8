"""
进度管理和断点续传模块
负责跟踪导入进度，支持断点续传功能
"""

import json
import os
import time
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
import logging

from config import get_config

logger = logging.getLogger(__name__)

class ProgressManager:
    def __init__(self, config_name=None):
        """
        初始化进度管理器
        
        Args:
            config_name: 配置名称
        """
        self.config = get_config(config_name)
        self.progress_file = self.config.PROGRESS_FILE
        self.lock = threading.Lock()
        
        # 进度数据结构
        self.progress_data = {
            'session_id': None,
            'start_time': None,
            'last_update': None,
            'total_files': 0,
            'processed_files': 0,
            'total_records': 0,
            'processed_records': 0,
            'failed_records': 0,
            'current_file': None,
            'file_progress': {},  # 文件级别的进度
            'completed_files': [],  # 已完成的文件列表
            'failed_files': [],   # 失败的文件列表
            'status': 'idle'  # idle, running, paused, completed, failed
        }
        
        # 加载现有进度
        self.load_progress()
    
    def generate_session_id(self):
        """
        生成新的会话ID
        
        Returns:
            str: 会话ID
        """
        return f"session_{int(time.time())}"
    
    def start_new_session(self, total_files=0, total_records=0):
        """
        开始新的导入会话
        
        Args:
            total_files: 总文件数
            total_records: 总记录数（估算）
        """
        with self.lock:
            self.progress_data = {
                'session_id': self.generate_session_id(),
                'start_time': datetime.now().isoformat(),
                'last_update': datetime.now().isoformat(),
                'total_files': total_files,
                'processed_files': 0,
                'total_records': total_records,
                'processed_records': 0,
                'failed_records': 0,
                'current_file': None,
                'file_progress': {},
                'completed_files': [],
                'failed_files': [],
                'status': 'running'
            }
            
            self.save_progress()
            logger.info(f"开始新的导入会话: {self.progress_data['session_id']}")
    
    def resume_session(self):
        """
        恢复现有会话
        
        Returns:
            bool: 是否成功恢复会话
        """
        if not self.progress_file.exists():
            logger.info("没有找到进度文件，无法恢复会话")
            return False
        
        with self.lock:
            if self.progress_data['status'] in ['completed', 'idle']:
                logger.info("上次导入已完成，无需恢复")
                return False
            
            self.progress_data['status'] = 'running'
            self.progress_data['last_update'] = datetime.now().isoformat()
            self.save_progress()
            
            logger.info(f"恢复导入会话: {self.progress_data['session_id']}")
            logger.info(f"已处理文件: {self.progress_data['processed_files']}/{self.progress_data['total_files']}")
            logger.info(f"已处理记录: {self.progress_data['processed_records']}")
            
            return True
    
    def update_file_progress(self, file_name, status, records_processed=0, records_failed=0):
        """
        更新文件级别的进度
        
        Args:
            file_name: 文件名
            status: 文件状态 (processing, completed, failed, skipped)
            records_processed: 已处理记录数
            records_failed: 失败记录数
        """
        with self.lock:
            self.progress_data['current_file'] = file_name
            self.progress_data['last_update'] = datetime.now().isoformat()
            
            # 更新文件进度
            self.progress_data['file_progress'][file_name] = {
                'status': status,
                'records_processed': records_processed,
                'records_failed': records_failed,
                'timestamp': datetime.now().isoformat()
            }
            
            # 更新总体进度
            if status == 'completed':
                if file_name not in self.progress_data['completed_files']:
                    self.progress_data['completed_files'].append(file_name)
                    self.progress_data['processed_files'] += 1
                
                self.progress_data['processed_records'] += records_processed
                self.progress_data['failed_records'] += records_failed
                
            elif status == 'failed':
                if file_name not in self.progress_data['failed_files']:
                    self.progress_data['failed_files'].append(file_name)
                    self.progress_data['processed_files'] += 1
                
                self.progress_data['failed_records'] += records_failed
            
            elif status == 'skipped':
                if file_name not in self.progress_data['completed_files']:
                    self.progress_data['completed_files'].append(file_name)
                    self.progress_data['processed_files'] += 1
                
                self.progress_data['processed_records'] += records_processed
            
            # 保存进度
            self.save_progress()
    
    def update_records_progress(self, processed_delta=0, failed_delta=0):
        """
        更新记录级别的进度
        
        Args:
            processed_delta: 新处理的记录数
            failed_delta: 新失败的记录数
        """
        with self.lock:
            self.progress_data['processed_records'] += processed_delta
            self.progress_data['failed_records'] += failed_delta
            self.progress_data['last_update'] = datetime.now().isoformat()
    
    def complete_session(self, status='completed'):
        """
        完成导入会话
        
        Args:
            status: 最终状态 (completed, failed, cancelled)
        """
        with self.lock:
            self.progress_data['status'] = status
            self.progress_data['last_update'] = datetime.now().isoformat()
            self.progress_data['current_file'] = None
            
            self.save_progress()
            
            logger.info(f"导入会话完成: {status}")
            logger.info(f"总计处理: {self.progress_data['processed_records']} 条记录")
            logger.info(f"失败记录: {self.progress_data['failed_records']} 条")
    
    def get_remaining_files(self, all_files):
        """
        获取剩余需要处理的文件列表
        
        Args:
            all_files: 所有文件列表
            
        Returns:
            list: 剩余文件列表
        """
        with self.lock:
            completed_files = set(self.progress_data['completed_files'])
            
            # 获取文件名（不包含路径）
            remaining_files = []
            for file_path in all_files:
                file_name = Path(file_path).name
                if file_name not in completed_files:
                    remaining_files.append(file_path)
            
            return remaining_files
    
    def is_file_completed(self, file_name):
        """
        检查文件是否已完成
        
        Args:
            file_name: 文件名
            
        Returns:
            bool: 是否已完成
        """
        with self.lock:
            return file_name in self.progress_data['completed_files']
    
    def get_progress_summary(self):
        """
        获取进度摘要
        
        Returns:
            dict: 进度摘要信息
        """
        with self.lock:
            progress_percent = 0
            if self.progress_data['total_files'] > 0:
                progress_percent = (self.progress_data['processed_files'] / self.progress_data['total_files']) * 100
            
            elapsed_time = 0
            if self.progress_data['start_time']:
                start_time = datetime.fromisoformat(self.progress_data['start_time'])
                elapsed_time = (datetime.now() - start_time).total_seconds()
            
            # 计算处理速度
            speed = 0
            if elapsed_time > 0:
                speed = self.progress_data['processed_records'] / elapsed_time
            
            # 估算剩余时间
            eta = 0
            if speed > 0 and self.progress_data['total_records'] > 0:
                remaining_records = self.progress_data['total_records'] - self.progress_data['processed_records']
                eta = remaining_records / speed
            
            return {
                'session_id': self.progress_data['session_id'],
                'status': self.progress_data['status'],
                'progress_percent': round(progress_percent, 2),
                'total_files': self.progress_data['total_files'],
                'processed_files': self.progress_data['processed_files'],
                'remaining_files': self.progress_data['total_files'] - self.progress_data['processed_files'],
                'total_records': self.progress_data['total_records'],
                'processed_records': self.progress_data['processed_records'],
                'failed_records': self.progress_data['failed_records'],
                'current_file': self.progress_data['current_file'],
                'elapsed_time': round(elapsed_time, 2),
                'processing_speed': round(speed, 2),
                'eta_seconds': round(eta, 2),
                'start_time': self.progress_data['start_time'],
                'last_update': self.progress_data['last_update']
            }
    
    def save_progress(self):
        """
        保存进度到文件
        """
        try:
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存进度文件时出错: {e}")
    
    def load_progress(self):
        """
        从文件加载进度
        """
        try:
            if self.progress_file.exists():
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)
                    self.progress_data.update(loaded_data)
                    logger.info(f"加载进度文件: {self.progress_file}")
            else:
                logger.info("进度文件不存在，使用默认进度")
        except Exception as e:
            logger.error(f"加载进度文件时出错: {e}")
            # 使用默认进度数据
    
    def clear_progress(self):
        """
        清除进度数据
        """
        with self.lock:
            if self.progress_file.exists():
                os.remove(self.progress_file)
            
            self.progress_data = {
                'session_id': None,
                'start_time': None,
                'last_update': None,
                'total_files': 0,
                'processed_files': 0,
                'total_records': 0,
                'processed_records': 0,
                'failed_records': 0,
                'current_file': None,
                'file_progress': {},
                'completed_files': [],
                'failed_files': [],
                'status': 'idle'
            }
            
            logger.info("进度数据已清除")

if __name__ == "__main__":
    # 测试进度管理器
    pm = ProgressManager()
    
    # 开始新会话
    pm.start_new_session(total_files=10, total_records=100000)
    
    # 模拟文件处理
    pm.update_file_progress("test1.csv", "processing")
    pm.update_file_progress("test1.csv", "completed", 5000, 10)
    
    # 获取进度摘要
    summary = pm.get_progress_summary()
    print("进度摘要:", summary)
