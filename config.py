"""
系统配置文件
包含数据库连接、文件路径、导入参数等配置
"""

import os
from pathlib import Path

class Config:
    """系统配置类"""
    
    # 数据库配置
    MONGODB_URI = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
    DATABASE_NAME = os.getenv('DATABASE_NAME', 'judgment_documents')
    COLLECTION_NAME = 'documents'
    
    # 文件路径配置
    BASE_DIR = Path(__file__).parent
    DATA_DIR = BASE_DIR / '裁判文书'
    LOGS_DIR = BASE_DIR / 'logs'
    STATIC_DIR = BASE_DIR / 'static'
    TEMPLATES_DIR = BASE_DIR / 'templates'
    
    # 确保目录存在
    LOGS_DIR.mkdir(exist_ok=True)
    STATIC_DIR.mkdir(exist_ok=True)
    TEMPLATES_DIR.mkdir(exist_ok=True)
    
    # 导入配置
    BATCH_SIZE = 1000  # 批量插入大小
    MAX_WORKERS = 4    # 最大工作线程数
    CHUNK_SIZE = 10000 # CSV文件分块读取大小
    
    # Web应用配置
    FLASK_HOST = '0.0.0.0'
    FLASK_PORT = 5000
    FLASK_DEBUG = True
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here')
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE = LOGS_DIR / 'import.log'
    
    # 进度跟踪配置
    PROGRESS_FILE = BASE_DIR / 'import_progress.json'
    CHECKPOINT_INTERVAL = 1000  # 每处理多少条记录保存一次检查点
    
    # CSV文件配置
    CSV_ENCODING = 'utf-8'
    CSV_DELIMITER = ','
    
    # CSV字段映射（CSV字段名 -> MongoDB字段名）
    FIELD_MAPPING = {
        '原始链接': 'original_url',
        '案号': 'case_number',
        '案件名称': 'case_name',
        '法院': 'court',
        '所属地区': 'region',
        '案件类型': 'case_type',
        '案件类型编码': 'case_type_code',
        '来源': 'source',
        '审理程序': 'trial_procedure',
        '裁判日期': 'judgment_date',
        '公开日期': 'publish_date',
        '当事人': 'parties',
        '案由': 'case_cause',
        '法律依据': 'legal_basis',
        '全文': 'full_text'
    }
    
    # 日期字段列表
    DATE_FIELDS = ['judgment_date', 'publish_date']
    
    # 需要提取关键词的字段
    KEYWORD_FIELDS = ['case_name', 'case_cause']
    
    # WebSocket配置
    WEBSOCKET_PING_INTERVAL = 10
    WEBSOCKET_PING_TIMEOUT = 5
    
    # 性能监控配置
    MONITOR_INTERVAL = 5  # 秒
    MEMORY_THRESHOLD = 80  # 内存使用率阈值（百分比）
    
    # 错误处理配置
    MAX_RETRY_ATTEMPTS = 3
    RETRY_DELAY = 1  # 秒
    
    # 数据验证配置
    REQUIRED_FIELDS = ['case_number', 'case_name', 'court']
    MAX_TEXT_LENGTH = 1000000  # 全文最大长度
    
    @classmethod
    def get_data_files(cls):
        """
        获取所有需要导入的CSV文件列表
        
        Returns:
            list: 文件路径列表，按年份和月份排序
        """
        files = []
        
        if not cls.DATA_DIR.exists():
            return files
        
        # 遍历年份目录
        for year_dir in sorted(cls.DATA_DIR.iterdir()):
            if year_dir.is_dir() and year_dir.name.endswith('年'):
                # 遍历该年份下的CSV文件
                for csv_file in sorted(year_dir.glob('*.csv')):
                    files.append(csv_file)
        
        return files
    
    @classmethod
    def get_file_info(cls, file_path):
        """
        从文件路径提取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            dict: 包含年份、月份等信息
        """
        file_path = Path(file_path)
        file_name = file_path.name
        
        # 从文件名提取年份和月份
        # 例如: "2021年01月裁判文书数据.csv"
        try:
            parts = file_name.replace('.csv', '').split('年')
            year = int(parts[0])
            month_part = parts[1].replace('月裁判文书数据', '')
            month = int(month_part)
            
            return {
                'year': year,
                'month': month,
                'file_name': file_name,
                'file_path': str(file_path),
                'file_size': file_path.stat().st_size if file_path.exists() else 0
            }
        except (ValueError, IndexError):
            return {
                'year': None,
                'month': None,
                'file_name': file_name,
                'file_path': str(file_path),
                'file_size': file_path.stat().st_size if file_path.exists() else 0
            }
    
    @classmethod
    def validate_config(cls):
        """
        验证配置是否正确
        
        Returns:
            tuple: (is_valid, error_messages)
        """
        errors = []
        
        # 检查数据目录
        if not cls.DATA_DIR.exists():
            errors.append(f"数据目录不存在: {cls.DATA_DIR}")
        
        # 检查是否有CSV文件
        csv_files = cls.get_data_files()
        if not csv_files:
            errors.append("未找到任何CSV文件")
        
        # 检查批量大小
        if cls.BATCH_SIZE <= 0:
            errors.append("批量大小必须大于0")
        
        # 检查工作线程数
        if cls.MAX_WORKERS <= 0:
            errors.append("工作线程数必须大于0")
        
        return len(errors) == 0, errors

# 开发环境配置
class DevelopmentConfig(Config):
    FLASK_DEBUG = True
    LOG_LEVEL = 'DEBUG'

# 生产环境配置
class ProductionConfig(Config):
    FLASK_DEBUG = False
    LOG_LEVEL = 'WARNING'
    BATCH_SIZE = 2000
    MAX_WORKERS = 8

# 测试环境配置
class TestingConfig(Config):
    DATABASE_NAME = 'judgment_documents_test'
    BATCH_SIZE = 100
    LOG_LEVEL = 'DEBUG'

# 根据环境变量选择配置
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config(config_name=None):
    """
    获取配置对象
    
    Args:
        config_name: 配置名称
        
    Returns:
        Config: 配置对象
    """
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'default')
    
    return config_map.get(config_name, DevelopmentConfig)

if __name__ == "__main__":
    # 测试配置
    config = get_config()
    
    print("配置信息:")
    print(f"数据库URI: {config.MONGODB_URI}")
    print(f"数据库名称: {config.DATABASE_NAME}")
    print(f"数据目录: {config.DATA_DIR}")
    print(f"批量大小: {config.BATCH_SIZE}")
    
    # 验证配置
    is_valid, errors = config.validate_config()
    if is_valid:
        print("配置验证通过")
    else:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    
    # 显示文件列表
    files = config.get_data_files()
    print(f"\n找到 {len(files)} 个CSV文件:")
    for file_path in files[:5]:  # 只显示前5个
        info = config.get_file_info(file_path)
        print(f"  {info['file_name']} - {info['year']}年{info['month']}月")
    
    if len(files) > 5:
        print(f"  ... 还有 {len(files) - 5} 个文件")
