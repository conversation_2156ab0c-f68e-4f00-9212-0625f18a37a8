# 裁判文书数据导入系统使用说明

## 系统概述

这是一个完整的裁判文书数据导入系统，具有以下特性：

✅ **MongoDB数据存储** - 优化的数据结构和索引设计  
✅ **Web可视化界面** - 实时监控导入进度和系统状态  
✅ **断点续传功能** - 支持从中断位置继续导入  
✅ **批量处理** - 高效的批量插入和并发处理  
✅ **实时统计** - 处理速度、进度百分比、剩余时间估算  
✅ **错误处理** - 完善的错误处理和日志记录  
✅ **健康检查** - 系统组件健康状态监控  

## 快速开始

### 1. 启动系统
双击 `start.bat` 文件，系统会自动：
- 激活Python虚拟环境
- 检查系统健康状态
- 启动Web服务器

### 2. 访问Web界面
在浏览器中打开：http://localhost:5000

### 3. 使用Web界面控制导入

#### 主要功能按钮：
- **开始导入** - 开始全新的数据导入
- **断点续传** - 从上次中断位置继续导入
- **停止导入** - 暂停当前导入过程
- **清除进度** - 重置所有进度数据

#### 监控面板：
- **实时进度条** - 显示导入进度百分比
- **处理速度** - 每秒处理的记录数
- **文件列表** - 显示所有CSV文件及其状态
- **数据库统计** - 当前数据库中的记录数量
- **年度分布图** - 按年份显示数据分布
- **实时日志** - 显示系统运行日志

## 命令行使用

### 基本命令
```bash
# 激活虚拟环境
venv\Scripts\activate

# 检查系统健康状态
python main.py health

# 初始化数据库
python main.py init

# 开始数据导入
python main.py import

# 启动Web界面
python main.py web
```

### 高级选项
```bash
# 重新开始导入（不使用断点续传）
python main.py import --no-resume

# 使用生产环境配置
python main.py web --config production
```

## 数据文件要求

### 文件结构
```
裁判文书/
├── 2021年/
│   ├── 2021年01月裁判文书数据.csv
│   ├── 2021年02月裁判文书数据.csv
│   └── ...
├── 2022年/
├── 2023年/
└── 2024年/
```

### CSV文件格式
文件必须包含以下字段：
- 原始链接
- 案号
- 案件名称
- 法院
- 所属地区
- 案件类型
- 案件类型编码
- 来源
- 审理程序
- 裁判日期
- 公开日期
- 当事人
- 案由
- 法律依据
- 全文

## 系统状态说明

### 导入状态
- **空闲** (灰色) - 系统未在导入
- **运行中** (蓝色，闪烁) - 正在导入数据
- **已完成** (绿色) - 导入成功完成
- **失败** (红色) - 导入过程中出错
- **已暂停** (橙色) - 导入被用户暂停

### 文件状态
- **未导入** - 文件尚未处理
- **已导入** - 文件已成功导入到数据库

## 性能优化建议

### 系统配置
- **内存**: 建议8GB以上
- **磁盘**: 确保有足够空间存储数据
- **MongoDB**: 建议使用SSD硬盘

### 导入参数调优
在 `config.py` 中可以调整：
- `BATCH_SIZE`: 批量插入大小（默认1000）
- `MAX_WORKERS`: 最大工作线程数（默认4）
- `CHUNK_SIZE`: CSV文件分块读取大小（默认10000）

## 故障排除

### 常见问题

1. **MongoDB连接失败**
   - 确保MongoDB服务正在运行
   - 检查连接字符串是否正确

2. **内存不足**
   - 减少BATCH_SIZE和CHUNK_SIZE
   - 减少MAX_WORKERS数量

3. **文件读取错误**
   - 检查CSV文件编码（应为UTF-8）
   - 验证文件格式是否正确

4. **导入速度慢**
   - 增加BATCH_SIZE（在内存允许范围内）
   - 检查磁盘I/O性能
   - 优化MongoDB配置

### 日志查看
```bash
# 查看实时日志
tail -f logs/import.log

# 查看错误日志
type logs\errors.json
```

## 数据库信息

### 连接信息
- **数据库**: judgment_documents
- **集合**: documents
- **索引**: 自动创建13个优化索引

### 统计查询
系统提供以下统计功能：
- 按年份统计记录数量
- 按地区和案件类型分析
- 导入进度跟踪
- 文件处理状态

## 技术支持

如果遇到问题，请：
1. 查看实时日志了解详细错误信息
2. 运行健康检查确认系统状态
3. 检查MongoDB服务状态
4. 确认数据文件格式正确

## 系统架构

```
Web界面 (Flask + SocketIO)
    ↓
数据导入器 (data_importer.py)
    ↓
进度管理器 (progress_manager.py)
    ↓
MongoDB数据库 (documents集合)
```

系统采用模块化设计，各组件职责清晰，便于维护和扩展。
