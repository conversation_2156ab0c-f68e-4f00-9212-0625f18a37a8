#!/usr/bin/env python3
"""
裁判文书数据导入系统主启动脚本
"""

import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import get_config
from database_design import DatabaseDesign
from data_importer import DataImporter
from progress_manager import ProgressManager
from error_handler import get_logger, handle_exceptions, health_checker
from web_app import app, socketio, initialize_components

logger = get_logger(__name__)

def setup_health_checks():
    """设置健康检查"""
    
    def check_database():
        """检查数据库连接"""
        try:
            config = get_config()
            db_design = DatabaseDesign(
                connection_string=config.MONGODB_URI,
                db_name=config.DATABASE_NAME
            )
            
            # 尝试获取集合统计信息
            stats = db_design.get_collection_stats()
            return True, f"数据库连接正常，文档数量: {stats.get('document_count', 0)}"
            
        except Exception as e:
            return False, f"数据库连接失败: {e}"
    
    def check_data_files():
        """检查数据文件"""
        try:
            config = get_config()
            files = config.get_data_files()
            
            if not files:
                return False, "未找到任何CSV数据文件"
            
            # 检查文件是否可读
            readable_files = 0
            for file_path in files:
                if Path(file_path).exists() and Path(file_path).is_file():
                    readable_files += 1
            
            if readable_files == 0:
                return False, "所有数据文件都不可读"
            
            return True, f"找到 {len(files)} 个数据文件，{readable_files} 个可读"
            
        except Exception as e:
            return False, f"检查数据文件失败: {e}"
    
    def check_disk_space():
        """检查磁盘空间"""
        try:
            import shutil
            
            config = get_config()
            total, used, free = shutil.disk_usage(config.BASE_DIR)
            
            free_gb = free / (1024**3)
            
            if free_gb < 1:  # 少于1GB
                return False, f"磁盘空间不足: {free_gb:.2f} GB"
            
            return True, f"磁盘空间充足: {free_gb:.2f} GB 可用"
            
        except Exception as e:
            return False, f"检查磁盘空间失败: {e}"
    
    # 注册健康检查
    health_checker.register_check("database", check_database, 30)
    health_checker.register_check("data_files", check_data_files, 60)
    health_checker.register_check("disk_space", check_disk_space, 120)

@handle_exceptions(reraise=True)
def init_database():
    """初始化数据库"""
    logger.info("初始化数据库...")
    
    config = get_config()
    db_design = DatabaseDesign(
        connection_string=config.MONGODB_URI,
        db_name=config.DATABASE_NAME
    )
    
    # 初始化数据库和索引
    db_design.initialize_database()
    
    # 显示统计信息
    stats = db_design.get_collection_stats()
    logger.info(f"数据库初始化完成，当前文档数量: {stats.get('document_count', 0)}")
    
    return True

@handle_exceptions(reraise=True)
def run_import(resume=True):
    """运行数据导入"""
    logger.info("开始数据导入...")
    
    # 创建导入器和进度管理器
    importer = DataImporter()
    progress_manager = ProgressManager()
    
    # 获取文件列表
    config = get_config()
    all_files = config.get_data_files()
    
    if not all_files:
        logger.error("未找到任何CSV文件")
        return False
    
    logger.info(f"找到 {len(all_files)} 个文件")
    
    # 估算总记录数
    estimated_records = importer.estimate_total_records()
    logger.info(f"估算总记录数: {estimated_records}")
    
    try:
        if resume and progress_manager.progress_data['status'] in ['running', 'paused']:
            # 恢复会话
            progress_manager.resume_session()
            remaining_files = progress_manager.get_remaining_files(all_files)
            logger.info(f"恢复导入，剩余 {len(remaining_files)} 个文件")
        else:
            # 开始新会话
            progress_manager.start_new_session(
                total_files=len(all_files),
                total_records=estimated_records
            )
            remaining_files = all_files
            logger.info(f"开始新的导入会话，共 {len(remaining_files)} 个文件")
        
        # 导入文件
        for i, file_path in enumerate(remaining_files):
            file_name = config.get_file_info(file_path)['file_name']
            logger.info(f"处理文件 {i+1}/{len(remaining_files)}: {file_name}")
            
            # 更新文件进度
            progress_manager.update_file_progress(file_name, 'processing')
            
            # 导入文件
            result = importer.import_file(file_path)
            
            # 更新文件进度
            if result.get('skipped'):
                progress_manager.update_file_progress(
                    file_name, 'skipped',
                    records_processed=result.get('success_count', 0)
                )
                logger.info(f"文件已跳过: {file_name}")
            elif result.get('error'):
                progress_manager.update_file_progress(
                    file_name, 'failed',
                    records_failed=result.get('error_count', 0)
                )
                logger.error(f"文件导入失败: {file_name} - {result.get('error')}")
            else:
                progress_manager.update_file_progress(
                    file_name, 'completed',
                    records_processed=result.get('success_count', 0),
                    records_failed=result.get('error_count', 0)
                )
                logger.info(f"文件导入完成: {file_name} - 成功: {result.get('success_count', 0)}, 失败: {result.get('error_count', 0)}")
        
        # 完成会话
        progress_manager.complete_session('completed')
        logger.info("数据导入完成")
        
        # 显示最终统计
        summary = progress_manager.get_progress_summary()
        logger.info(f"导入总结:")
        logger.info(f"  处理文件: {summary['processed_files']}/{summary['total_files']}")
        logger.info(f"  成功记录: {summary['processed_records']}")
        logger.info(f"  失败记录: {summary['failed_records']}")
        logger.info(f"  总用时: {summary['elapsed_time']:.2f} 秒")
        
        return True
        
    except Exception as e:
        logger.error(f"导入过程中出错: {e}")
        progress_manager.complete_session('failed')
        return False

@handle_exceptions(reraise=True)
def run_web_server():
    """运行Web服务器"""
    logger.info("启动Web服务器...")
    
    # 初始化组件
    initialize_components()
    
    # 设置健康检查
    setup_health_checks()
    
    # 运行健康检查
    health_status = health_checker.get_health_status()
    if not health_status['healthy']:
        logger.warning("系统健康检查发现问题:")
        for check_name, result in health_status['checks'].items():
            if not result['healthy']:
                logger.warning(f"  {check_name}: {result['message']}")
    else:
        logger.info("系统健康检查通过")
    
    # 启动Web应用
    config = get_config()
    logger.info(f"Web服务器启动在 http://{config.FLASK_HOST}:{config.FLASK_PORT}")
    
    socketio.run(
        app,
        host=config.FLASK_HOST,
        port=config.FLASK_PORT,
        debug=config.FLASK_DEBUG
    )

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='裁判文书数据导入系统')
    parser.add_argument('command', choices=['init', 'import', 'web', 'health'], 
                       help='要执行的命令')
    parser.add_argument('--config', default='development',
                       choices=['development', 'production', 'testing'],
                       help='配置环境')
    parser.add_argument('--no-resume', action='store_true',
                       help='不使用断点续传，重新开始导入')
    
    args = parser.parse_args()
    
    # 设置环境变量
    os.environ['FLASK_ENV'] = args.config
    
    logger.info(f"启动裁判文书数据导入系统 - 环境: {args.config}")
    
    try:
        if args.command == 'init':
            # 初始化数据库
            success = init_database()
            sys.exit(0 if success else 1)
            
        elif args.command == 'import':
            # 运行数据导入
            success = run_import(resume=not args.no_resume)
            sys.exit(0 if success else 1)
            
        elif args.command == 'web':
            # 运行Web服务器
            run_web_server()
            
        elif args.command == 'health':
            # 运行健康检查
            setup_health_checks()
            health_status = health_checker.get_health_status()
            
            print(f"系统健康状态: {'健康' if health_status['healthy'] else '异常'}")
            print(f"检查时间: {health_status['timestamp']}")
            print("\n详细检查结果:")
            
            for check_name, result in health_status['checks'].items():
                status = '✓' if result['healthy'] else '✗'
                print(f"  {status} {check_name}: {result['message']}")
            
            sys.exit(0 if health_status['healthy'] else 1)
            
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
