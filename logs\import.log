2025-08-09 12:07:47,831 - __main__ - INFO - 启动裁判文书数据导入系统 - 环境: development
2025-08-09 12:07:47,833 - pymongo.topology - DEBUG - {"message": "Starting topology monitoring", "topologyId": {"$oid": "6896c9937f56180726e4e84d"}}
2025-08-09 12:07:47,833 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "6896c9937f56180726e4e84d"}, "previousDescription": "<TopologyDescription id: 6896c9937f56180726e4e84d, topology_type: Unknown, servers: []>", "newDescription": "<TopologyDescription id: 6896c9937f56180726e4e84d, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None>]>"}
2025-08-09 12:07:47,833 - pymongo.topology - DEBUG - {"message": "Starting server monitoring", "topologyId": {"$oid": "6896c9937f56180726e4e84d"}, "serverHost": "localhost", "serverPort": 27017}
2025-08-09 12:07:47,834 - pymongo.connection - DEBUG - {"message": "Connection pool created", "clientId": {"$oid": "6896c9937f56180726e4e84d"}, "serverHost": "localhost", "serverPort": 27017}
2025-08-09 12:07:47,835 - pymongo.serverSelection - DEBUG - {"message": "Server selection started", "selector": "Primary()", "operation": "collStats", "topologyDescription": "<TopologyDescription id: 6896c9937f56180726e4e84d, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "6896c9937f56180726e4e84d"}}
2025-08-09 12:07:47,835 - pymongo.serverSelection - DEBUG - {"message": "Waiting for suitable server to become available", "selector": "Primary()", "operation": "collStats", "topologyDescription": "<TopologyDescription id: 6896c9937f56180726e4e84d, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None>]>", "clientId": {"$oid": "6896c9937f56180726e4e84d"}, "remainingTimeMS": 30000}
2025-08-09 12:07:47,859 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "6896c9937f56180726e4e84d"}, "driverConnectionId": 1, "serverHost": "localhost", "serverPort": 27017, "awaited": false}
2025-08-09 12:07:47,859 - pymongo.topology - DEBUG - {"message": "Server heartbeat succeeded", "topologyId": {"$oid": "6896c9937f56180726e4e84d"}, "driverConnectionId": 1, "serverConnectionId": 46, "serverHost": "localhost", "serverPort": 27017, "awaited": false, "durationMS": 0.0, "reply": "{\"helloOk\": true, \"ismaster\": true, \"topologyVersion\": {\"processId\": {\"$oid\": \"6896be6d9fff7ac2d59f54b3\"}}, \"maxBsonObjectSize\": 16777216, \"maxMessageSizeBytes\": 48000000, \"maxWriteBatchSize\": 100000, \"localTime\": {\"$date\": \"2025-08-09T04:07:47.860Z\"}, \"logicalSessionTimeoutMinutes\": 30, \"connectionId\": 46, \"maxWireVersion\": 25, \"ok\": 1.0}"}
2025-08-09 12:07:47,861 - pymongo.connection - DEBUG - {"message": "Connection pool ready", "clientId": {"$oid": "6896c9937f56180726e4e84d"}, "serverHost": "localhost", "serverPort": 27017}
2025-08-09 12:07:47,861 - pymongo.topology - DEBUG - {"message": "Topology description changed", "topologyId": {"$oid": "6896c9937f56180726e4e84d"}, "previousDescription": "<TopologyDescription id: 6896c9937f56180726e4e84d, topology_type: Unknown, servers: [<ServerDescription ('localhost', 27017) server_type: Unknown, rtt: None>]>", "newDescription": "<TopologyDescription id: 6896c9937f56180726e4e84d, topology_type: Single, servers: [<ServerDescription ('localhost', 27017) server_type: Standalone, rtt: 0.0>]>"}
2025-08-09 12:07:47,861 - pymongo.serverSelection - DEBUG - {"message": "Server selection succeeded", "selector": "Primary()", "operation": "collStats", "topologyDescription": "<TopologyDescription id: 6896c9937f56180726e4e84d, topology_type: Single, servers: [<ServerDescription ('localhost', 27017) server_type: Standalone, rtt: 0.0>]>", "clientId": {"$oid": "6896c9937f56180726e4e84d"}, "serverHost": "localhost", "serverPort": 27017}
2025-08-09 12:07:47,862 - pymongo.topology - DEBUG - {"message": "Server heartbeat started", "topologyId": {"$oid": "6896c9937f56180726e4e84d"}, "driverConnectionId": 1, "serverConnectionId": 46, "serverHost": "localhost", "serverPort": 27017, "awaited": true}
2025-08-09 12:07:47,863 - pymongo.connection - DEBUG - {"message": "Connection checkout started", "clientId": {"$oid": "6896c9937f56180726e4e84d"}, "serverHost": "localhost", "serverPort": 27017}
2025-08-09 12:07:47,864 - pymongo.connection - DEBUG - {"message": "Connection created", "clientId": {"$oid": "6896c9937f56180726e4e84d"}, "serverHost": "localhost", "serverPort": 27017, "driverConnectionId": 1}
2025-08-09 12:07:47,865 - pymongo.connection - DEBUG - {"message": "Connection ready", "clientId": {"$oid": "6896c9937f56180726e4e84d"}, "serverHost": "localhost", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.0}
2025-08-09 12:07:47,865 - pymongo.connection - DEBUG - {"message": "Connection checked out", "clientId": {"$oid": "6896c9937f56180726e4e84d"}, "serverHost": "localhost", "serverPort": 27017, "driverConnectionId": 1, "durationMS": 0.0}
2025-08-09 12:07:47,867 - pymongo.command - DEBUG - {"message": "Command started", "clientId": {"$oid": "6896c9937f56180726e4e84d"}, "command": "{\"collStats\": \"documents\", \"lsid\": {\"id\": {\"$binary\": {\"base64\": \"IWjwidrBSZOavn6CYki0nA==\", \"subType\": \"04\"}}}, \"$db\": \"judgment_documents\"}", "commandName": "collStats", "databaseName": "judgment_documents", "requestId": 18467, "operationId": 18467, "driverConnectionId": 1, "serverConnectionId": 48, "serverHost": "localhost", "serverPort": 27017}
2025-08-09 12:07:47,883 - pymongo.command - DEBUG - {"message": "Command succeeded", "clientId": {"$oid": "6896c9937f56180726e4e84d"}, "durationMS": 16.031, "reply": "{\"ns\": \"judgment_documents.documents\", \"size\": 37455958172, \"count\": 9347679, \"avgObjSize\": 4006, \"storageSize\": 17817210880, \"freeStorageSize\": 1744896, \"wiredTiger\": {\"metadata\": {\"formatVersion\": 1}, \"creationString\": \"access_pattern_hint=none,allocation_size=4KB,app_metadata=(formatVersion=1),assert=(commit_timestamp=none,durable_timestamp=none,read_timestamp=none,write_timestamp=off),block_allocation=best,block_compressor=snappy,cache_resident=false,checksum=on,colgroups=,collator=,columns=,dictionary=0,encryption=(keyid=,name=),exclusive=false,extractor=,format=btree,huffman_key=,huffman_value=,ignore_in_memory_cache_size=false,immutable=false,import=(compare_timestamp=oldest_timestamp,enabled=false,file_metadata=,metadata_file=,panic_corrupt=true,repair=false),internal_item_max=0,internal_key_max=0,internal_key_truncate=true,internal_page_max=4KB,key_format=q,key_gap=10,leaf_item_max=0,leaf_key_max=0,leaf_page_max=32KB,leaf_value_max=64MB,log=(enabled=true),lsm=(auto_throttle=tr...", "commandName": "collStats", "databaseName": "judgment_documents", "requestId": 18467, "operationId": 18467, "driverConnectionId": 1, "serverConnectionId": 48, "serverHost": "localhost", "serverPort": 27017}
2025-08-09 12:07:47,884 - pymongo.connection - DEBUG - {"message": "Connection checked in", "clientId": {"$oid": "6896c9937f56180726e4e84d"}, "serverHost": "localhost", "serverPort": 27017, "driverConnectionId": 1}
