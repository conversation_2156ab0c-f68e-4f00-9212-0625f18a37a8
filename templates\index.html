<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>裁判文书数据导入系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    <style>
        .status-card {
            transition: all 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .progress-bar {
            transition: width 0.5s ease;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
        }
        .metric-label {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .log-container {
            height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
        .file-item {
            transition: background-color 0.3s ease;
        }
        .file-item:hover {
            background-color: #f8f9fa;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-idle { background-color: #6c757d; }
        .status-running { background-color: #0d6efd; animation: pulse 1.5s infinite; }
        .status-completed { background-color: #198754; }
        .status-failed { background-color: #dc3545; }
        .status-paused { background-color: #fd7e14; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 标题栏 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center py-3 border-bottom">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-database-fill text-primary"></i>
                        裁判文书数据导入系统
                    </h1>
                    <div class="d-flex gap-2">
                        <button id="startBtn" class="btn btn-success">
                            <i class="bi bi-play-fill"></i> 开始导入
                        </button>
                        <button id="resumeBtn" class="btn btn-warning">
                            <i class="bi bi-arrow-clockwise"></i> 断点续传
                        </button>
                        <button id="stopBtn" class="btn btn-danger" disabled>
                            <i class="bi bi-stop-fill"></i> 停止导入
                        </button>
                        <button id="clearBtn" class="btn btn-secondary">
                            <i class="bi bi-trash"></i> 清除进度
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 状态卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card status-card h-100">
                    <div class="card-body text-center">
                        <div class="metric-value text-primary" id="totalFiles">0</div>
                        <div class="metric-label">总文件数</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card h-100">
                    <div class="card-body text-center">
                        <div class="metric-value text-success" id="processedFiles">0</div>
                        <div class="metric-label">已处理文件</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card h-100">
                    <div class="card-body text-center">
                        <div class="metric-value text-info" id="totalRecords">0</div>
                        <div class="metric-label">总记录数</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card h-100">
                    <div class="card-body text-center">
                        <div class="metric-value text-warning" id="processingSpeed">0</div>
                        <div class="metric-label">处理速度 (条/秒)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 进度条和状态 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="card-title mb-0">
                                <span class="status-indicator" id="statusIndicator"></span>
                                导入进度
                            </h5>
                            <div class="text-muted">
                                <span id="currentFile">等待开始...</span>
                            </div>
                        </div>
                        
                        <div class="progress mb-3" style="height: 20px;">
                            <div class="progress-bar progress-bar-striped" id="progressBar" 
                                 role="progressbar" style="width: 0%">
                                <span id="progressText">0%</span>
                            </div>
                        </div>
                        
                        <div class="row text-center">
                            <div class="col-md-3">
                                <small class="text-muted">已用时间</small>
                                <div id="elapsedTime">00:00:00</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">预计剩余</small>
                                <div id="remainingTime">--:--:--</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">成功记录</small>
                                <div id="successRecords">0</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">失败记录</small>
                                <div id="failedRecords">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 文件列表 -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-file-earmark-text"></i>
                            文件列表
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive" style="max-height: 500px;">
                            <table class="table table-hover mb-0">
                                <thead class="table-light sticky-top">
                                    <tr>
                                        <th>文件名</th>
                                        <th>年月</th>
                                        <th>大小</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody id="fileList">
                                    <!-- 文件列表将通过JavaScript填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计图表和日志 -->
            <div class="col-md-6">
                <!-- 数据库统计 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-bar-chart"></i>
                            数据库统计
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="h4 text-primary mb-0" id="dbRecordCount">0</div>
                                    <small class="text-muted">数据库记录数</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="h4 text-info mb-0" id="dbStorageSize">0 MB</div>
                                    <small class="text-muted">存储大小</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 年度统计图表 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-graph-up"></i>
                            年度数据分布
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="yearlyChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 实时日志 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-terminal"></i>
                            实时日志
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="log-container" id="logContainer">
                            <div class="text-muted">等待日志信息...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
