"""
裁判文书数据库设计模块
定义MongoDB集合结构和索引
"""

from pymongo import MongoClient, ASCENDING, DESCENDING, TEXT
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseDesign:
    def __init__(self, connection_string="mongodb://localhost:27017/", db_name="judgment_documents"):
        """
        初始化数据库设计类
        
        Args:
            connection_string: MongoDB连接字符串
            db_name: 数据库名称
        """
        self.client = MongoClient(connection_string)
        self.db = self.client[db_name]
        self.collection_name = "documents"
        self.collection = self.db[self.collection_name]
        
    def get_document_schema(self):
        """
        定义裁判文书文档结构
        基于CSV文件的字段：原始链接,案号,案件名称,法院,所属地区,案件类型,案件类型编码,来源,审理程序,裁判日期,公开日期,当事人,案由,法律依据,全文
        """
        schema = {
            "_id": "ObjectId",  # MongoDB自动生成
            "original_url": "String",  # 原始链接
            "case_number": "String",   # 案号
            "case_name": "String",     # 案件名称
            "court": "String",         # 法院
            "region": "String",        # 所属地区
            "case_type": "String",     # 案件类型
            "case_type_code": "String", # 案件类型编码
            "source": "String",        # 来源
            "trial_procedure": "String", # 审理程序
            "judgment_date": "Date",   # 裁判日期
            "publish_date": "Date",    # 公开日期
            "parties": "String",       # 当事人
            "case_cause": "String",    # 案由
            "legal_basis": "String",   # 法律依据
            "full_text": "String",     # 全文
            
            # 额外的元数据字段
            "import_time": "Date",     # 导入时间
            "file_source": "String",   # 来源文件名
            "year": "Integer",         # 年份（从裁判日期提取）
            "month": "Integer",        # 月份（从裁判日期提取）
            "text_length": "Integer",  # 全文长度
            "keywords": ["String"],    # 关键词（从案件名称和案由提取）
        }
        return schema
    
    def create_indexes(self):
        """
        创建索引以优化查询性能
        """
        try:
            # 1. 案号索引（唯一索引，防止重复导入）
            self.collection.create_index([("case_number", ASCENDING)], unique=True, background=True)
            logger.info("创建案号唯一索引")
            
            # 2. 复合索引：年份+月份（用于时间范围查询）
            self.collection.create_index([("year", ASCENDING), ("month", ASCENDING)], background=True)
            logger.info("创建年份月份复合索引")
            
            # 3. 法院索引
            self.collection.create_index([("court", ASCENDING)], background=True)
            logger.info("创建法院索引")
            
            # 4. 所属地区索引
            self.collection.create_index([("region", ASCENDING)], background=True)
            logger.info("创建地区索引")
            
            # 5. 案件类型索引
            self.collection.create_index([("case_type", ASCENDING)], background=True)
            logger.info("创建案件类型索引")
            
            # 6. 审理程序索引
            self.collection.create_index([("trial_procedure", ASCENDING)], background=True)
            logger.info("创建审理程序索引")
            
            # 7. 裁判日期索引（用于时间排序）
            self.collection.create_index([("judgment_date", DESCENDING)], background=True)
            logger.info("创建裁判日期索引")
            
            # 8. 公开日期索引
            self.collection.create_index([("publish_date", DESCENDING)], background=True)
            logger.info("创建公开日期索引")
            
            # 9. 案由索引
            self.collection.create_index([("case_cause", ASCENDING)], background=True)
            logger.info("创建案由索引")
            
            # 10. 全文搜索索引
            self.collection.create_index([
                ("case_name", TEXT),
                ("full_text", TEXT),
                ("parties", TEXT),
                ("case_cause", TEXT)
            ], background=True)
            logger.info("创建全文搜索索引")
            
            # 11. 复合索引：地区+案件类型+年份（用于统计分析）
            self.collection.create_index([
                ("region", ASCENDING),
                ("case_type", ASCENDING),
                ("year", ASCENDING)
            ], background=True)
            logger.info("创建地区案件类型年份复合索引")
            
            # 12. 导入时间索引（用于追踪导入进度）
            self.collection.create_index([("import_time", DESCENDING)], background=True)
            logger.info("创建导入时间索引")
            
            # 13. 文件来源索引（用于断点续传）
            self.collection.create_index([("file_source", ASCENDING)], background=True)
            logger.info("创建文件来源索引")
            
            logger.info("所有索引创建完成")
            
        except Exception as e:
            logger.error(f"创建索引时出错: {e}")
            raise
    
    def get_collection_stats(self):
        """
        获取集合统计信息
        """
        try:
            stats = self.db.command("collStats", self.collection_name)
            return {
                "document_count": stats.get("count", 0),
                "storage_size": stats.get("storageSize", 0),
                "index_count": stats.get("nindexes", 0),
                "total_index_size": stats.get("totalIndexSize", 0),
                "avg_obj_size": stats.get("avgObjSize", 0)
            }
        except Exception as e:
            logger.error(f"获取集合统计信息时出错: {e}")
            return {}
    
    def get_import_progress(self):
        """
        获取导入进度信息
        """
        try:
            pipeline = [
                {
                    "$group": {
                        "_id": "$file_source",
                        "count": {"$sum": 1},
                        "last_import": {"$max": "$import_time"}
                    }
                },
                {
                    "$sort": {"last_import": -1}
                }
            ]
            
            result = list(self.collection.aggregate(pipeline))
            return result
            
        except Exception as e:
            logger.error(f"获取导入进度时出错: {e}")
            return []
    
    def check_file_imported(self, file_name):
        """
        检查文件是否已经导入
        
        Args:
            file_name: 文件名
            
        Returns:
            dict: 包含是否已导入和导入数量的信息
        """
        try:
            count = self.collection.count_documents({"file_source": file_name})
            return {
                "imported": count > 0,
                "count": count
            }
        except Exception as e:
            logger.error(f"检查文件导入状态时出错: {e}")
            return {"imported": False, "count": 0}
    
    def get_yearly_stats(self):
        """
        获取按年份统计的数据
        """
        try:
            pipeline = [
                {
                    "$group": {
                        "_id": "$year",
                        "count": {"$sum": 1},
                        "case_types": {"$addToSet": "$case_type"}
                    }
                },
                {
                    "$sort": {"_id": 1}
                }
            ]
            
            result = list(self.collection.aggregate(pipeline))
            return result
            
        except Exception as e:
            logger.error(f"获取年度统计时出错: {e}")
            return []
    
    def initialize_database(self):
        """
        初始化数据库，创建集合和索引
        """
        logger.info("开始初始化数据库...")
        
        # 创建集合（如果不存在）
        if self.collection_name not in self.db.list_collection_names():
            self.db.create_collection(self.collection_name)
            logger.info(f"创建集合: {self.collection_name}")
        
        # 创建索引
        self.create_indexes()
        
        logger.info("数据库初始化完成")
        
        return True

if __name__ == "__main__":
    # 测试数据库设计
    db_design = DatabaseDesign()
    
    # 初始化数据库
    db_design.initialize_database()
    
    # 显示集合统计信息
    stats = db_design.get_collection_stats()
    print("集合统计信息:", stats)
    
    # 显示文档结构
    schema = db_design.get_document_schema()
    print("文档结构:", schema)
