"""
Web应用主模块
提供数据导入的Web界面和API
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_socketio import SocketIO, emit
import threading
import time
import json
from datetime import datetime
import logging

from config import get_config
from data_importer import DataImporter
from progress_manager import ProgressManager
from database_design import DatabaseDesign

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
config = get_config()
app.config['SECRET_KEY'] = config.SECRET_KEY

# 创建SocketIO实例
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局变量
data_importer = None
progress_manager = None
db_design = None
import_thread = None

def initialize_components():
    """初始化组件"""
    global data_importer, progress_manager, db_design
    
    data_importer = DataImporter()
    progress_manager = ProgressManager()
    db_design = DatabaseDesign(
        connection_string=config.MONGODB_URI,
        db_name=config.DATABASE_NAME
    )
    
    # 设置状态回调
    data_importer.set_status_callback(broadcast_status)

def broadcast_status(status):
    """广播状态更新"""
    socketio.emit('status_update', status)

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """获取当前状态"""
    try:
        # 获取导入状态
        import_status = data_importer.get_import_status() if data_importer else {}
        
        # 获取进度摘要
        progress_summary = progress_manager.get_progress_summary() if progress_manager else {}
        
        # 获取数据库统计
        db_stats = db_design.get_collection_stats() if db_design else {}
        
        # 获取文件列表
        files = config.get_data_files()
        file_info = []
        for file_path in files:
            info = config.get_file_info(file_path)
            # 检查文件是否已导入
            if progress_manager:
                info['completed'] = progress_manager.is_file_completed(info['file_name'])
            else:
                info['completed'] = False
            file_info.append(info)
        
        return jsonify({
            'import_status': import_status,
            'progress_summary': progress_summary,
            'database_stats': db_stats,
            'files': file_info,
            'total_files': len(files)
        })
        
    except Exception as e:
        logger.error(f"获取状态时出错: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/start_import', methods=['POST'])
def start_import():
    """开始导入"""
    global import_thread
    
    try:
        if data_importer and data_importer.is_importing:
            return jsonify({'error': '导入正在进行中'}), 400
        
        # 获取参数
        data = request.get_json() or {}
        resume = data.get('resume', True)
        
        # 开始导入线程
        import_thread = threading.Thread(target=run_import, args=(resume,))
        import_thread.daemon = True
        import_thread.start()
        
        return jsonify({'message': '导入已开始', 'resume': resume})
        
    except Exception as e:
        logger.error(f"开始导入时出错: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/stop_import', methods=['POST'])
def stop_import():
    """停止导入"""
    try:
        if data_importer:
            data_importer.stop_import()
        
        if progress_manager:
            progress_manager.complete_session('cancelled')
        
        return jsonify({'message': '导入已停止'})
        
    except Exception as e:
        logger.error(f"停止导入时出错: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/clear_progress', methods=['POST'])
def clear_progress():
    """清除进度"""
    try:
        if data_importer and data_importer.is_importing:
            return jsonify({'error': '导入正在进行中，无法清除进度'}), 400
        
        if progress_manager:
            progress_manager.clear_progress()
        
        return jsonify({'message': '进度已清除'})
        
    except Exception as e:
        logger.error(f"清除进度时出错: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/database_stats')
def get_database_stats():
    """获取数据库统计信息"""
    try:
        if not db_design:
            return jsonify({'error': '数据库未初始化'}), 500
        
        # 基本统计
        stats = db_design.get_collection_stats()
        
        # 年度统计
        yearly_stats = db_design.get_yearly_stats()
        
        # 导入进度统计
        import_progress = db_design.get_import_progress()
        
        return jsonify({
            'collection_stats': stats,
            'yearly_stats': yearly_stats,
            'import_progress': import_progress
        })
        
    except Exception as e:
        logger.error(f"获取数据库统计时出错: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/files')
def get_files():
    """获取文件列表"""
    try:
        files = config.get_data_files()
        file_info = []
        
        for file_path in files:
            info = config.get_file_info(file_path)
            
            # 检查文件是否已导入
            if db_design:
                import_status = db_design.check_file_imported(info['file_name'])
                info['imported'] = import_status['imported']
                info['imported_count'] = import_status['count']
            else:
                info['imported'] = False
                info['imported_count'] = 0
            
            # 检查进度管理器中的状态
            if progress_manager:
                info['completed'] = progress_manager.is_file_completed(info['file_name'])
            else:
                info['completed'] = False
            
            file_info.append(info)
        
        return jsonify({
            'files': file_info,
            'total_files': len(files)
        })
        
    except Exception as e:
        logger.error(f"获取文件列表时出错: {e}")
        return jsonify({'error': str(e)}), 500

def run_import(resume=True):
    """运行导入任务"""
    try:
        logger.info(f"开始导入任务，resume={resume}")
        
        # 获取文件列表
        all_files = config.get_data_files()
        
        if not all_files:
            logger.error("未找到任何文件")
            return
        
        # 估算总记录数
        estimated_records = data_importer.estimate_total_records()
        
        if resume and progress_manager.progress_data['status'] in ['running', 'paused']:
            # 恢复会话
            progress_manager.resume_session()
            remaining_files = progress_manager.get_remaining_files(all_files)
        else:
            # 开始新会话
            progress_manager.start_new_session(
                total_files=len(all_files),
                total_records=estimated_records
            )
            remaining_files = all_files
        
        logger.info(f"需要处理 {len(remaining_files)} 个文件")
        
        # 导入文件
        for file_path in remaining_files:
            if not data_importer.is_importing:
                logger.info("导入被停止")
                break
            
            file_name = config.get_file_info(file_path)['file_name']
            
            # 更新文件进度
            progress_manager.update_file_progress(file_name, 'processing')
            
            # 导入文件
            result = data_importer.import_file(file_path)
            
            # 更新文件进度
            if result.get('skipped'):
                progress_manager.update_file_progress(
                    file_name, 'skipped',
                    records_processed=result.get('success_count', 0)
                )
            elif result.get('error'):
                progress_manager.update_file_progress(
                    file_name, 'failed',
                    records_failed=result.get('error_count', 0)
                )
            else:
                progress_manager.update_file_progress(
                    file_name, 'completed',
                    records_processed=result.get('success_count', 0),
                    records_failed=result.get('error_count', 0)
                )
        
        # 完成会话
        if data_importer.is_importing:
            progress_manager.complete_session('completed')
            logger.info("导入任务完成")
        else:
            progress_manager.complete_session('cancelled')
            logger.info("导入任务被取消")
        
    except Exception as e:
        logger.error(f"导入任务出错: {e}")
        if progress_manager:
            progress_manager.complete_session('failed')

@socketio.on('connect')
def handle_connect():
    """处理客户端连接"""
    logger.info('客户端已连接')
    
    # 发送当前状态
    if data_importer and progress_manager:
        status = data_importer.get_import_status()
        progress = progress_manager.get_progress_summary()
        emit('status_update', {'import_status': status, 'progress_summary': progress})

@socketio.on('disconnect')
def handle_disconnect():
    """处理客户端断开连接"""
    logger.info('客户端已断开连接')

@socketio.on('request_status')
def handle_request_status():
    """处理状态请求"""
    try:
        status = data_importer.get_import_status() if data_importer else {}
        progress = progress_manager.get_progress_summary() if progress_manager else {}
        db_stats = db_design.get_collection_stats() if db_design else {}
        
        emit('status_update', {
            'import_status': status,
            'progress_summary': progress,
            'database_stats': db_stats
        })
    except Exception as e:
        logger.error(f"处理状态请求时出错: {e}")
        emit('error', {'message': str(e)})

if __name__ == '__main__':
    # 初始化组件
    initialize_components()
    
    # 启动应用
    logger.info(f"启动Web应用，地址: http://{config.FLASK_HOST}:{config.FLASK_PORT}")
    socketio.run(
        app,
        host=config.FLASK_HOST,
        port=config.FLASK_PORT,
        debug=config.FLASK_DEBUG
    )
