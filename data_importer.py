"""
数据导入核心模块
负责CSV文件读取、数据清洗和MongoDB批量插入
"""

import pandas as pd
import numpy as np
from pymongo import MongoClient
from pymongo.errors import BulkWriteError, DuplicateKeyError
from datetime import datetime, timedelta
import logging
import json
import time
import re
from pathlib import Path
from typing import Dict, List, Optional, Generator, Tuple
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

from config import get_config
from database_design import DatabaseDesign

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataImporter:
    def __init__(self, config_name=None):
        """
        初始化数据导入器
        
        Args:
            config_name: 配置名称
        """
        self.config = get_config(config_name)
        self.db_design = DatabaseDesign(
            connection_string=self.config.MONGODB_URI,
            db_name=self.config.DATABASE_NAME
        )
        
        # 导入状态
        self.is_importing = False
        self.current_file = None
        self.total_files = 0
        self.processed_files = 0
        self.total_records = 0
        self.processed_records = 0
        self.failed_records = 0
        self.start_time = None
        self.current_speed = 0
        
        # 线程锁
        self.lock = threading.Lock()
        
        # 状态回调函数
        self.status_callback = None
        
        # 初始化数据库
        self.db_design.initialize_database()
    
    def set_status_callback(self, callback):
        """
        设置状态更新回调函数
        
        Args:
            callback: 回调函数，接收状态字典参数
        """
        self.status_callback = callback
    
    def _update_status(self, **kwargs):
        """
        更新状态并调用回调函数
        """
        if self.status_callback:
            status = self.get_import_status()
            status.update(kwargs)
            self.status_callback(status)
    
    def parse_date(self, date_str):
        """
        解析日期字符串
        
        Args:
            date_str: 日期字符串
            
        Returns:
            datetime: 解析后的日期对象，解析失败返回None
        """
        if pd.isna(date_str) or not date_str:
            return None
        
        # 常见日期格式
        date_formats = [
            '%Y-%m-%d',
            '%Y/%m/%d',
            '%Y年%m月%d日',
            '%Y-%m-%d %H:%M:%S',
            '%Y/%m/%d %H:%M:%S'
        ]
        
        for fmt in date_formats:
            try:
                return datetime.strptime(str(date_str).strip(), fmt)
            except ValueError:
                continue
        
        logger.warning(f"无法解析日期: {date_str}")
        return None
    
    def extract_keywords(self, text):
        """
        从文本中提取关键词
        
        Args:
            text: 输入文本
            
        Returns:
            list: 关键词列表
        """
        if pd.isna(text) or not text:
            return []
        
        # 简单的关键词提取（可以后续优化为更复杂的NLP处理）
        text = str(text).strip()
        
        # 移除常见的停用词
        stop_words = {'与', '和', '及', '或', '的', '了', '在', '是', '有', '为', '等', '案', '件'}
        
        # 使用正则表达式分割文本
        words = re.findall(r'[\u4e00-\u9fff]+', text)
        
        # 过滤停用词和短词
        keywords = [word for word in words if len(word) >= 2 and word not in stop_words]
        
        return list(set(keywords))  # 去重
    
    def clean_record(self, record, file_info):
        """
        清洗单条记录
        
        Args:
            record: 原始记录字典
            file_info: 文件信息
            
        Returns:
            dict: 清洗后的记录，如果记录无效返回None
        """
        try:
            # 创建新记录
            cleaned_record = {}
            
            # 映射字段名
            for csv_field, mongo_field in self.config.FIELD_MAPPING.items():
                value = record.get(csv_field, '')
                
                # 处理空值
                if pd.isna(value):
                    value = ''
                else:
                    value = str(value).strip()
                
                cleaned_record[mongo_field] = value
            
            # 验证必需字段
            for field in self.config.REQUIRED_FIELDS:
                if not cleaned_record.get(field):
                    logger.warning(f"记录缺少必需字段 {field}: {cleaned_record.get('case_number', 'Unknown')}")
                    return None
            
            # 处理日期字段
            for field in self.config.DATE_FIELDS:
                if field in cleaned_record:
                    cleaned_record[field] = self.parse_date(cleaned_record[field])
            
            # 提取年份和月份
            judgment_date = cleaned_record.get('judgment_date')
            if judgment_date:
                cleaned_record['year'] = judgment_date.year
                cleaned_record['month'] = judgment_date.month
            else:
                # 从文件信息中获取年份和月份
                cleaned_record['year'] = file_info.get('year')
                cleaned_record['month'] = file_info.get('month')
            
            # 提取关键词
            keywords = []
            for field in self.config.KEYWORD_FIELDS:
                if field in cleaned_record:
                    keywords.extend(self.extract_keywords(cleaned_record[field]))
            cleaned_record['keywords'] = list(set(keywords))
            
            # 计算全文长度
            full_text = cleaned_record.get('full_text', '')
            cleaned_record['text_length'] = len(full_text)
            
            # 限制全文长度
            if len(full_text) > self.config.MAX_TEXT_LENGTH:
                cleaned_record['full_text'] = full_text[:self.config.MAX_TEXT_LENGTH]
                logger.warning(f"全文过长，已截断: {cleaned_record.get('case_number', 'Unknown')}")
            
            # 添加元数据
            cleaned_record['import_time'] = datetime.now()
            cleaned_record['file_source'] = file_info['file_name']
            
            return cleaned_record
            
        except Exception as e:
            logger.error(f"清洗记录时出错: {e}")
            return None
    
    def read_csv_file(self, file_path):
        """
        读取CSV文件
        
        Args:
            file_path: CSV文件路径
            
        Yields:
            dict: 记录字典
        """
        try:
            # 获取文件信息
            file_info = self.config.get_file_info(file_path)
            
            logger.info(f"开始读取文件: {file_path}")
            
            # 分块读取CSV文件
            chunk_iter = pd.read_csv(
                file_path,
                encoding=self.config.CSV_ENCODING,
                delimiter=self.config.CSV_DELIMITER,
                chunksize=self.config.CHUNK_SIZE,
                dtype=str,  # 所有字段都作为字符串读取
                na_values=['', 'NULL', 'null', 'None', 'none'],
                keep_default_na=False
            )
            
            for chunk in chunk_iter:
                for _, row in chunk.iterrows():
                    record = row.to_dict()
                    cleaned_record = self.clean_record(record, file_info)
                    
                    if cleaned_record:
                        yield cleaned_record
                    else:
                        with self.lock:
                            self.failed_records += 1
                        
        except Exception as e:
            logger.error(f"读取CSV文件时出错 {file_path}: {e}")
            raise
    
    def batch_insert(self, records):
        """
        批量插入记录到MongoDB
        
        Args:
            records: 记录列表
            
        Returns:
            tuple: (成功数量, 失败数量)
        """
        if not records:
            return 0, 0
        
        try:
            # 批量插入
            result = self.db_design.collection.insert_many(records, ordered=False)
            return len(result.inserted_ids), 0
            
        except BulkWriteError as e:
            # 处理批量写入错误
            success_count = e.details.get('nInserted', 0)
            error_count = len(records) - success_count
            
            # 记录重复键错误
            duplicate_count = 0
            for error in e.details.get('writeErrors', []):
                if error.get('code') == 11000:  # 重复键错误
                    duplicate_count += 1
            
            logger.warning(f"批量插入部分失败: 成功 {success_count}, 失败 {error_count}, 重复 {duplicate_count}")
            return success_count, error_count
            
        except Exception as e:
            logger.error(f"批量插入时出错: {e}")
            return 0, len(records)
    
    def import_file(self, file_path):
        """
        导入单个文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            dict: 导入结果统计
        """
        file_info = self.config.get_file_info(file_path)
        
        # 检查文件是否已经导入
        import_status = self.db_design.check_file_imported(file_info['file_name'])
        if import_status['imported']:
            logger.info(f"文件已导入，跳过: {file_info['file_name']} (已有 {import_status['count']} 条记录)")
            return {
                'file_name': file_info['file_name'],
                'success_count': import_status['count'],
                'error_count': 0,
                'skipped': True
            }
        
        logger.info(f"开始导入文件: {file_info['file_name']}")
        
        with self.lock:
            self.current_file = file_info['file_name']
        
        self._update_status(current_file=file_info['file_name'])
        
        batch = []
        success_count = 0
        error_count = 0
        batch_count = 0
        
        start_time = time.time()
        
        try:
            for record in self.read_csv_file(file_path):
                batch.append(record)
                
                # 批量插入
                if len(batch) >= self.config.BATCH_SIZE:
                    batch_success, batch_error = self.batch_insert(batch)
                    success_count += batch_success
                    error_count += batch_error
                    batch_count += 1
                    
                    with self.lock:
                        self.processed_records += batch_success
                        self.failed_records += batch_error
                    
                    # 计算速度
                    elapsed = time.time() - start_time
                    if elapsed > 0:
                        self.current_speed = success_count / elapsed
                    
                    self._update_status(
                        processed_records=self.processed_records,
                        failed_records=self.failed_records,
                        current_speed=self.current_speed
                    )
                    
                    batch = []
                    
                    logger.info(f"已处理批次 {batch_count}, 成功: {success_count}, 失败: {error_count}")
            
            # 处理剩余记录
            if batch:
                batch_success, batch_error = self.batch_insert(batch)
                success_count += batch_success
                error_count += batch_error
                
                with self.lock:
                    self.processed_records += batch_success
                    self.failed_records += batch_error
            
            logger.info(f"文件导入完成: {file_info['file_name']}, 成功: {success_count}, 失败: {error_count}")
            
            return {
                'file_name': file_info['file_name'],
                'success_count': success_count,
                'error_count': error_count,
                'skipped': False
            }
            
        except Exception as e:
            logger.error(f"导入文件时出错 {file_path}: {e}")
            return {
                'file_name': file_info['file_name'],
                'success_count': success_count,
                'error_count': error_count + len(batch),
                'error': str(e),
                'skipped': False
            }
    
    def get_import_status(self):
        """
        获取导入状态
        
        Returns:
            dict: 状态信息
        """
        with self.lock:
            elapsed_time = 0
            if self.start_time:
                elapsed_time = time.time() - self.start_time
            
            progress = 0
            if self.total_files > 0:
                progress = (self.processed_files / self.total_files) * 100
            
            eta = 0
            if self.current_speed > 0 and self.total_records > 0:
                remaining_records = self.total_records - self.processed_records
                eta = remaining_records / self.current_speed
            
            return {
                'is_importing': self.is_importing,
                'current_file': self.current_file,
                'total_files': self.total_files,
                'processed_files': self.processed_files,
                'total_records': self.total_records,
                'processed_records': self.processed_records,
                'failed_records': self.failed_records,
                'progress_percent': round(progress, 2),
                'elapsed_time': round(elapsed_time, 2),
                'current_speed': round(self.current_speed, 2),
                'eta_seconds': round(eta, 2)
            }

    def import_all_files(self, resume=True):
        """
        导入所有文件

        Args:
            resume: 是否从断点继续

        Returns:
            dict: 导入结果统计
        """
        files = self.config.get_data_files()

        if not files:
            logger.error("未找到任何CSV文件")
            return {'error': '未找到任何CSV文件'}

        with self.lock:
            self.is_importing = True
            self.total_files = len(files)
            self.processed_files = 0
            self.total_records = 0
            self.processed_records = 0
            self.failed_records = 0
            self.start_time = time.time()

        logger.info(f"开始导入 {len(files)} 个文件")
        self._update_status()

        results = []

        try:
            for i, file_path in enumerate(files):
                if not self.is_importing:  # 检查是否被停止
                    logger.info("导入被用户停止")
                    break

                result = self.import_file(file_path)
                results.append(result)

                with self.lock:
                    self.processed_files += 1

                self._update_status(processed_files=self.processed_files)

                logger.info(f"进度: {self.processed_files}/{self.total_files} 文件")

        except Exception as e:
            logger.error(f"导入过程中出错: {e}")
            results.append({'error': str(e)})

        finally:
            with self.lock:
                self.is_importing = False

            self._update_status(is_importing=False)

        # 统计总结果
        total_success = sum(r.get('success_count', 0) for r in results)
        total_error = sum(r.get('error_count', 0) for r in results)
        skipped_files = sum(1 for r in results if r.get('skipped', False))

        summary = {
            'total_files': len(files),
            'processed_files': len(results),
            'skipped_files': skipped_files,
            'total_success': total_success,
            'total_error': total_error,
            'results': results
        }

        logger.info(f"导入完成: 处理 {len(results)} 个文件, 成功 {total_success} 条, 失败 {total_error} 条")

        return summary

    def stop_import(self):
        """
        停止导入
        """
        with self.lock:
            self.is_importing = False

        logger.info("导入已停止")
        self._update_status(is_importing=False)

    def estimate_total_records(self):
        """
        估算总记录数（基于文件大小）

        Returns:
            int: 估算的总记录数
        """
        files = self.config.get_data_files()

        if not files:
            return 0

        # 使用第一个文件来估算平均每MB的记录数
        try:
            first_file = files[0]
            file_info = self.config.get_file_info(first_file)

            # 读取前1000行来估算
            sample_df = pd.read_csv(first_file, nrows=1000, encoding=self.config.CSV_ENCODING)
            sample_size = len(sample_df)

            if sample_size == 0:
                return 0

            # 估算每MB的记录数
            file_size_mb = file_info['file_size'] / (1024 * 1024)
            records_per_mb = sample_size / file_size_mb if file_size_mb > 0 else 1000

            # 计算所有文件的总记录数
            total_size_mb = sum(self.config.get_file_info(f)['file_size'] for f in files) / (1024 * 1024)
            estimated_records = int(total_size_mb * records_per_mb)

            logger.info(f"估算总记录数: {estimated_records}")
            return estimated_records

        except Exception as e:
            logger.error(f"估算记录数时出错: {e}")
            return 0

if __name__ == "__main__":
    # 测试数据导入器
    importer = DataImporter()

    # 获取文件列表
    files = importer.config.get_data_files()
    print(f"找到 {len(files)} 个文件")

    # 估算总记录数
    estimated = importer.estimate_total_records()
    print(f"估算总记录数: {estimated}")

    if files:
        # 测试导入第一个文件
        result = importer.import_file(files[0])
        print("导入结果:", result)
